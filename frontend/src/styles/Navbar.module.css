.navbar {
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  z-index: 1000;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 1rem;
  max-width: 1200px;
  margin: 0 auto;
  height: 60px;
}

.logo {
  font-size: 1.5rem;
  font-weight: bold;
}

.logoText {
  color: #10b981;
  cursor: pointer;
}

.navLinks {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.navLink {
  color: #65676b;
  text-decoration: none;
  font-weight: 500;
  position: relative;
  transition: color 0.2s;
}

.navLink:hover {
  color: #10b981;
}

.badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background-color: #e41e3f;
  color: white;
  font-size: 0.7rem;
  font-weight: bold;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.profileDropdown {
  position: relative;
}

.profileButton {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
}

.avatar {
  border-radius: 50%;
  object-fit: cover;
}

.avatarPlaceholder {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #10b981;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.dropdownContent {
  position: absolute;
  right: 0;
  top: 100%;
  background-color: white;
  min-width: 160px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  padding: 0.5rem 0;
  z-index: 1;
  display: none;
}

.profileDropdown:hover .dropdownContent {
  display: block;
}

.dropdownItem {
  display: block;
  padding: 0.5rem 1rem;
  color: #65676b;
  text-decoration: none;
  text-align: left;
  width: 100%;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.9rem;
}

.dropdownItem:hover {
  background-color: #f0f2f5;
  color: #10b981;
}

/* Mobile menu */
.mobileMenuButton {
  display: none;
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
}

.hamburger {
  display: block;
  width: 24px;
  height: 2px;
  background-color: #65676b;
  position: relative;
}

.hamburger::before,
.hamburger::after {
  content: '';
  position: absolute;
  width: 24px;
  height: 2px;
  background-color: #65676b;
  left: 0;
}

.hamburger::before {
  top: -6px;
}

.hamburger::after {
  bottom: -6px;
}

/* Media queries for responsive design */
@media (max-width: 768px) {
  .mobileMenuButton {
    display: block;
  }

  .navLinks {
    position: fixed;
    top: 60px;
    left: 0;
    right: 0;
    background-color: white;
    flex-direction: column;
    padding: 1rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    display: none;
    z-index: 999;
  }

  .navLinks.active {
    display: flex;
  }

  .profileDropdown {
    width: 100%;
  }

  .dropdownContent {
    position: static;
    box-shadow: none;
    display: block;
    margin-top: 0.5rem;
  }
}
